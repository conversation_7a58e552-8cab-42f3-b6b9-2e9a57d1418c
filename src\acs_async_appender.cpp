#include "acs_async_appender.h"
#include <log4cplus/helpers/loglog.h>
#include <log4cplus/helpers/stringhelper.h>
#include <log4cplus/spi/factory.h>

namespace acs {

AcsAsyncAppender::AcsAsyncAppender(const log4cplus::helpers::Properties& properties)
    : log4cplus::Appender(properties)
    , queue_size_(8192)
    , flush_interval_ms_(100)
    , shutdown_(false)
    , initialized_(false)
    , dropped_count_(0)
    , processed_count_(0) {
    
    parseProperties(properties);
    init();
}

AcsAsyncAppender::AcsAsyncAppender(
    log4cplus::SharedAppenderPtr backend_appender,
    size_t queue_size,
    int flush_interval_ms)
    : queue_size_(queue_size)
    , flush_interval_ms_(flush_interval_ms)
    , shutdown_(false)
    , initialized_(false)
    , dropped_count_(0)
    , processed_count_(0) {
    
    if (backend_appender) {
        addAppender(backend_appender);
    }
    init();
}

AcsAsyncAppender::~AcsAsyncAppender() {
    destructorImpl();
}

void AcsAsyncAppender::close() {
    if (!shutdown_.exchange(true)) {
        // 等待工作线程结束
        if (worker_ && worker_->joinable()) {
            worker_->join();
        }
        
        // 处理剩余的事件
        if (queue_) {
            processEvents();
        }
        
        // 关闭所有后端appender
        removeAllAppenders();
        
        closed = true;
    }
}

void AcsAsyncAppender::setQueueSize(size_t size) {
    if ((size & (size - 1)) != 0) {
        log4cplus::helpers::getLogLog().error(
            LOG4CPLUS_TEXT("AcsAsyncAppender: Queue size must be a power of 2"));
        return;
    }
    
    if (initialized_.load()) {
        log4cplus::helpers::getLogLog().warn(
            LOG4CPLUS_TEXT("AcsAsyncAppender: Cannot change queue size after initialization"));
        return;
    }
    
    queue_size_ = size;
}

void AcsAsyncAppender::setFlushInterval(int interval_ms) {
    if (interval_ms <= 0) {
        log4cplus::helpers::getLogLog().error(
            LOG4CPLUS_TEXT("AcsAsyncAppender: Flush interval must be positive"));
        return;
    }
    flush_interval_ms_ = interval_ms;
}

size_t AcsAsyncAppender::getCurrentQueueSize() const {
    return queue_ ? queue_->size() : 0;
}

void AcsAsyncAppender::append(const log4cplus::spi::InternalLoggingEvent& event) {
    if (shutdown_.load() || !queue_) {
        return;
    }
    
    // 尝试将事件加入队列
    if (!queue_->try_enqueue(event)) {
        // 队列已满，丢弃事件
        dropped_count_.fetch_add(1, std::memory_order_relaxed);
        
        // 记录警告（但要避免无限递归）
        static std::atomic<bool> warning_logged(false);
        if (!warning_logged.exchange(true)) {
            log4cplus::helpers::getLogLog().warn(
                LOG4CPLUS_TEXT("AcsAsyncAppender: Queue is full, dropping log events"));
        }
    }
}

void AcsAsyncAppender::init() {
    try {
        // 创建无锁队列
        queue_ = std::make_unique<LogEventQueue>(queue_size_);
        
        // 预分配批处理缓冲区
        batch_buffer_.reserve(BATCH_SIZE);
        
        // 启动工作线程
        worker_ = std::make_unique<std::thread>(&AcsAsyncAppender::workerThread, this);
        
        initialized_.store(true);
        
        log4cplus::helpers::getLogLog().debug(
            LOG4CPLUS_TEXT("AcsAsyncAppender: Initialized with queue size ") +
            log4cplus::helpers::convertIntegerToString(queue_size_));
            
    } catch (const std::exception& e) {
        log4cplus::helpers::getLogLog().error(
            LOG4CPLUS_TEXT("AcsAsyncAppender: Failed to initialize: ") +
            LOG4CPLUS_C_STR_TO_TSTRING(e.what()));
        throw;
    }
}

void AcsAsyncAppender::workerThread() {
    auto last_flush = std::chrono::steady_clock::now();
    const auto flush_interval = std::chrono::milliseconds(flush_interval_ms_);
    
    while (!shutdown_.load()) {
        // 处理队列中的事件
        size_t processed = processEvents();
        
        auto now = std::chrono::steady_clock::now();
        
        // 检查是否需要刷新
        if (now - last_flush >= flush_interval) {
            flushBackends();
            last_flush = now;
        }
        
        // 如果没有处理任何事件，短暂休眠避免CPU占用过高
        if (processed == 0) {
            std::this_thread::sleep_for(std::chrono::microseconds(100));
        }
    }
    
    // 关闭前最后一次处理和刷新
    processEvents();
    flushBackends();
}

size_t AcsAsyncAppender::processEvents() {
    if (!queue_) {
        return 0;
    }
    
    batch_buffer_.clear();
    log4cplus::spi::InternalLoggingEvent event;
    
    // 批量从队列中取出事件
    while (batch_buffer_.size() < BATCH_SIZE && queue_->try_dequeue(event)) {
        batch_buffer_.push_back(std::move(event));
    }
    
    // 批量处理事件
    for (const auto& evt : batch_buffer_) {
        appendToAppenders(evt);
    }
    
    size_t processed = batch_buffer_.size();
    if (processed > 0) {
        processed_count_.fetch_add(processed, std::memory_order_relaxed);
    }
    
    return processed;
}

void AcsAsyncAppender::flushBackends() {
    // 刷新所有后端appender（如果支持的话）
    auto appenders = getAllAppenders();
    for (auto& appender : appenders) {
        // 注意：log4cplus的Appender基类没有flush方法
        // 这里可以根据具体的appender类型进行特殊处理
        // 例如FileAppender可能有flush功能

        // 可以尝试动态转换为具体的appender类型来调用flush
        // 这里暂时不做特殊处理，因为不是所有appender都有flush方法
    }
}

void AcsAsyncAppender::parseProperties(const log4cplus::helpers::Properties& properties) {
    // 解析队列大小
    log4cplus::tstring queue_size_str = properties.getProperty(LOG4CPLUS_TEXT("QueueSize"));
    if (!queue_size_str.empty()) {
        try {
            size_t size = std::stoull(queue_size_str);
            setQueueSize(size);
        } catch (const std::exception&) {
            log4cplus::helpers::getLogLog().error(
                LOG4CPLUS_TEXT("AcsAsyncAppender: Invalid QueueSize value: ") + queue_size_str);
        }
    }
    
    // 解析刷新间隔
    log4cplus::tstring flush_interval_str = properties.getProperty(LOG4CPLUS_TEXT("FlushIntervalMs"));
    if (!flush_interval_str.empty()) {
        try {
            int interval = std::stoi(flush_interval_str);
            setFlushInterval(interval);
        } catch (const std::exception&) {
            log4cplus::helpers::getLogLog().error(
                LOG4CPLUS_TEXT("AcsAsyncAppender: Invalid FlushIntervalMs value: ") + flush_interval_str);
        }
    }
    
    // 解析后端appender
    log4cplus::tstring appender_name = properties.getProperty(LOG4CPLUS_TEXT("Appender"));
    if (!appender_name.empty()) {
        // 这里需要根据log4cplus的工厂模式来创建appender
        // 具体实现可能需要访问appender工厂
        log4cplus::helpers::getLogLog().debug(
            LOG4CPLUS_TEXT("AcsAsyncAppender: Backend appender configured: ") + appender_name);
    }
}

} // namespace acs
