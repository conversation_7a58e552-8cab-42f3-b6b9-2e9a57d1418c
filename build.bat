@echo off
setlocal

echo Building AcsAsyncAppender...

REM 创建构建目录
if not exist build mkdir build
cd build

REM 配置项目
echo Configuring project...
cmake .. -G "Visual Studio 16 2019" -A x64
if %errorlevel% neq 0 (
    echo Failed to configure project
    exit /b 1
)

REM 编译项目
echo Building project...
cmake --build . --config Release
if %errorlevel% neq 0 (
    echo Failed to build project
    exit /b 1
)

echo Build completed successfully!
echo.
echo Executables are located in:
echo   bin\Release\example.exe
echo   bin\Release\benchmark.exe
echo.
echo To run the example:
echo   cd build
echo   bin\Release\example.exe
echo.
echo To run the benchmark:
echo   cd build
echo   bin\Release\benchmark.exe

endlocal
