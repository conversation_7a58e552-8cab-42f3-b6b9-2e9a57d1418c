cmake_minimum_required(VERSION 3.10)
project(AcsAsyncAppender)

# 设置C++标准
set(CMAKE_CXX_STANDARD 14)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置编译选项
if(MSVC)
    add_compile_options(/W4)
    add_compile_definitions(_WIN32_WINNT=0x0601)
else()
    add_compile_options(-Wall -Wextra -O2)
endif()

# 查找log4cplus库
set(LOG4CPLUS_ROOT "${CMAKE_CURRENT_SOURCE_DIR}/third-parts/log4cplus")

# 设置包含目录
include_directories(
    ${LOG4CPLUS_ROOT}/include
    ${CMAKE_CURRENT_SOURCE_DIR}/src
)

# 设置库目录
link_directories(${LOG4CPLUS_ROOT}/libs)

# 创建AcsAsyncAppender库
add_library(AcsAsyncAppender STATIC
    src/acs_async_appender.cpp
    src/acs_async_appender.h
    src/lockfree_queue.h
)

# 链接log4cplus库
target_link_libraries(AcsAsyncAppender
    log4cplus
)

# 如果是Windows平台，还需要链接一些系统库
if(WIN32)
    target_link_libraries(AcsAsyncAppender
        ws2_32
        advapi32
    )
endif()

# 创建示例程序
add_executable(example
    src/example.cpp
)

target_link_libraries(example
    AcsAsyncAppender
    log4cplus
)

if(WIN32)
    target_link_libraries(example
        ws2_32
        advapi32
    )
endif()

# 创建基准测试程序
add_executable(benchmark
    src/benchmark.cpp
)

target_link_libraries(benchmark
    AcsAsyncAppender
    log4cplus
)

if(WIN32)
    target_link_libraries(benchmark
        ws2_32
        advapi32
    )
endif()

# 设置输出目录
set_target_properties(AcsAsyncAppender PROPERTIES
    ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib
)

set_target_properties(example benchmark PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# 复制log4cplus DLL到输出目录（Windows）
if(WIN32)
    add_custom_command(TARGET example POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
        "${LOG4CPLUS_ROOT}/libs/log4cplus.dll"
        $<TARGET_FILE_DIR:example>
    )

    add_custom_command(TARGET benchmark POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
        "${LOG4CPLUS_ROOT}/libs/log4cplus.dll"
        $<TARGET_FILE_DIR:benchmark>
    )
endif()

# 安装规则
install(TARGETS AcsAsyncAppender
    ARCHIVE DESTINATION lib
)

install(FILES
    src/acs_async_appender.h
    src/lockfree_queue.h
    DESTINATION include/acs
)

install(TARGETS example benchmark
    RUNTIME DESTINATION bin
)

# 打印配置信息
message(STATUS "AcsAsyncAppender Configuration:")
message(STATUS "  CMAKE_CXX_STANDARD: ${CMAKE_CXX_STANDARD}")
message(STATUS "  LOG4CPLUS_ROOT: ${LOG4CPLUS_ROOT}")
message(STATUS "  CMAKE_BUILD_TYPE: ${CMAKE_BUILD_TYPE}")
