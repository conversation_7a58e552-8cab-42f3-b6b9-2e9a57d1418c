#pragma once

#include <atomic>
#include <memory>
#include <cstddef>
#include <vector>
#include <type_traits>

namespace ulc {

/**
 * @brief 高性能无锁环形缓冲区队列
 * 
 * 基于spdlog的无锁队列设计，使用原子操作实现的无锁队列，
 * 支持多生产者单消费者模式(MPSC)，与spdlog性能对标
 * 
 * 特性：
 * - 无锁设计，避免线程阻塞
 * - 环形缓冲区，内存使用效率高
 * - 支持多生产者单消费者
 * - 基于原子操作，保证线程安全
 * - 与spdlog无锁队列性能对标
 */
template<typename T>
class lockfree_queue {
public:
    /**
     * @brief 构造函数
     * @param max_items 队列最大容量，必须是2的幂次方
     */
    explicit lockfree_queue(size_t max_items);

    /**
     * @brief 析构函数
     */
    ~lockfree_queue();

    // 禁用拷贝和赋值
    lockfree_queue(const lockfree_queue&) = delete;
    lockfree_queue& operator=(const lockfree_queue&) = delete;

    /**
     * @brief 尝试将元素入队
     * @param item 要入队的元素
     * @return true 成功入队，false 队列已满
     */
    bool enqueue(T&& item);

    /**
     * @brief 尝试将元素入队（拷贝版本）
     * @param item 要入队的元素
     * @return true 成功入队，false 队列已满
     */
    bool enqueue(const T& item);

    /**
     * @brief 尝试从队列中取出元素
     * @param popped_item 输出参数，存储取出的元素
     * @return true 成功取出，false 队列为空
     */
    bool dequeue(T& popped_item);

    /**
     * @brief 尝试从队列中一次性取出多个元素
     * @param popped_items 输出容器，存储取出的元素
     * @param max_count 最大取出数量
     * @return 实际取出的元素数量
     */
    size_t dequeue_bulk(std::vector<T>& popped_items, size_t max_count);

    /**
     * @brief 获取队列当前大小（近似值）
     * @return 队列大小
     */
    size_t size() const;

    /**
     * @brief 检查队列是否为空
     * @return true 队列为空，false 队列非空
     */
    bool empty() const;

    /**
     * @brief 获取队列容量
     * @return 队列容量
     */
    size_t capacity() const { return max_items_; }

    /**
     * @brief 检查队列是否已满
     * @return true 队列已满，false 队列未满
     */
    bool full() const;

private:
    struct alignas(64) slot_t {
        std::atomic<size_t> turn;
        T item;
    };

    // 确保是2的幂次方
    static size_t round_up_to_power_of_2(size_t n);

    const size_t max_items_;
    const size_t mask_;
    
    // 使用缓存行对齐避免false sharing
    alignas(64) std::atomic<size_t> head_;
    alignas(64) std::atomic<size_t> tail_;
    
    std::unique_ptr<slot_t[]> slots_;
};

// 模板实现
template<typename T>
lockfree_queue<T>::lockfree_queue(size_t max_items)
    : max_items_(round_up_to_power_of_2(max_items))
    , mask_(max_items_ - 1)
    , head_(0)
    , tail_(0)
    , slots_(std::make_unique<slot_t[]>(max_items_)) {
    
    // 初始化所有slot的turn
    for (size_t i = 0; i < max_items_; ++i) {
        slots_[i].turn.store(i, std::memory_order_relaxed);
    }
}

template<typename T>
lockfree_queue<T>::~lockfree_queue() = default;

template<typename T>
size_t lockfree_queue<T>::round_up_to_power_of_2(size_t n) {
    if (n == 0) return 1;
    
    // 检查是否已经是2的幂次方
    if ((n & (n - 1)) == 0) {
        return n;
    }
    
    // 找到下一个2的幂次方
    size_t power = 1;
    while (power < n) {
        power <<= 1;
    }
    return power;
}

template<typename T>
bool lockfree_queue<T>::enqueue(T&& item) {
    const size_t tail = tail_.load(std::memory_order_relaxed);
    slot_t& slot = slots_[tail & mask_];
    
    if (slot.turn.load(std::memory_order_acquire) != tail) {
        return false; // 队列已满
    }
    
    slot.item = std::move(item);
    slot.turn.store(tail + 1, std::memory_order_release);
    tail_.store(tail + 1, std::memory_order_relaxed);
    
    return true;
}

template<typename T>
bool lockfree_queue<T>::enqueue(const T& item) {
    const size_t tail = tail_.load(std::memory_order_relaxed);
    slot_t& slot = slots_[tail & mask_];
    
    if (slot.turn.load(std::memory_order_acquire) != tail) {
        return false; // 队列已满
    }
    
    slot.item = item;
    slot.turn.store(tail + 1, std::memory_order_release);
    tail_.store(tail + 1, std::memory_order_relaxed);
    
    return true;
}

template<typename T>
bool lockfree_queue<T>::dequeue(T& popped_item) {
    const size_t head = head_.load(std::memory_order_relaxed);
    slot_t& slot = slots_[head & mask_];
    
    if (slot.turn.load(std::memory_order_acquire) != head + 1) {
        return false; // 队列为空
    }
    
    popped_item = std::move(slot.item);
    slot.turn.store(head + max_items_, std::memory_order_release);
    head_.store(head + 1, std::memory_order_relaxed);
    
    return true;
}

template<typename T>
size_t lockfree_queue<T>::dequeue_bulk(std::vector<T>& popped_items, size_t max_count) {
    popped_items.clear();
    if (max_count == 0) {
        return 0;
    }
    
    popped_items.reserve(max_count);
    size_t count = 0;
    
    while (count < max_count) {
        const size_t head = head_.load(std::memory_order_relaxed);
        slot_t& slot = slots_[head & mask_];
        
        if (slot.turn.load(std::memory_order_acquire) != head + 1) {
            break; // 队列为空
        }
        
        popped_items.emplace_back(std::move(slot.item));
        slot.turn.store(head + max_items_, std::memory_order_release);
        head_.store(head + 1, std::memory_order_relaxed);
        ++count;
    }
    
    return count;
}

template<typename T>
size_t lockfree_queue<T>::size() const {
    const size_t tail = tail_.load(std::memory_order_acquire);
    const size_t head = head_.load(std::memory_order_acquire);
    return tail - head;
}

template<typename T>
bool lockfree_queue<T>::empty() const {
    return size() == 0;
}

template<typename T>
bool lockfree_queue<T>::full() const {
    return size() >= max_items_;
}

} // namespace ulc
