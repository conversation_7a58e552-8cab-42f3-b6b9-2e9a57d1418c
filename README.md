# AcsAsyncAppender

基于无锁队列的高性能log4cplus异步Appender实现，类似于spdlog的异步日志功能。

## 特性

- **无锁设计**: 使用原子操作实现的无锁环形缓冲区队列，避免线程阻塞
- **高性能**: 支持多生产者单消费者模式(MPSC)，优化内存访问模式
- **低延迟**: 日志记录操作几乎不阻塞主线程
- **可配置**: 支持配置队列大小、刷新间隔等参数
- **统计信息**: 提供处理事件数、丢弃事件数等统计信息
- **优雅关闭**: 支持优雅的关闭机制，确保日志不丢失

## 架构设计

```
应用线程1 ──┐
应用线程2 ──┼──> 无锁队列 ──> 工作线程 ──> 后端Appender ──> 文件/控制台
应用线程N ──┘
```

### 核心组件

1. **LockFreeQueue**: 无锁环形缓冲区队列
   - 基于原子操作的CAS算法
   - 支持多生产者单消费者
   - 内存对齐优化，避免false sharing

2. **AcsAsyncAppender**: 异步Appender实现
   - 继承log4cplus::Appender
   - 管理工作线程和队列
   - 支持批量处理提高效率

## 编译和安装

### 前提条件

- C++14或更高版本编译器
- CMake 3.10或更高版本
- log4cplus库（已包含在third-parts目录中）

### 编译步骤

```bash
# 创建构建目录
mkdir build
cd build

# 配置项目
cmake ..

# 编译
cmake --build .

# 运行示例
./bin/example
```

### Windows (Visual Studio)

```cmd
mkdir build
cd build
cmake .. -G "Visual Studio 16 2019"
cmake --build . --config Release
bin\Release\example.exe
```

## 使用方法

### 基本使用

```cpp
#include "acs_async_appender.h"
#include <log4cplus/fileappender.h>
#include <log4cplus/layout.h>

// 创建后端appender
auto file_appender = std::make_shared<log4cplus::FileAppender>(
    LOG4CPLUS_TEXT("app.log"));

// 设置布局
std::unique_ptr<log4cplus::Layout> layout(
    new log4cplus::PatternLayout(
        LOG4CPLUS_TEXT("%D{%Y-%m-%d %H:%M:%S.%q} [%t] %-5p %c - %m%n")));
file_appender->setLayout(std::move(layout));

// 创建异步appender
auto async_appender = std::make_shared<acs::AcsAsyncAppender>(
    file_appender,  // 后端appender
    8192,          // 队列大小
    100            // 刷新间隔(ms)
);

// 添加到logger
log4cplus::Logger logger = log4cplus::Logger::getInstance(
    LOG4CPLUS_TEXT("MyApp"));
logger.addAppender(async_appender);

// 记录日志
LOG4CPLUS_INFO(logger, "Hello, AcsAsyncAppender!");
```

### 配置文件使用

```properties
# log4cplus配置文件示例
log4cplus.rootLogger=INFO, AsyncAppender

log4cplus.appender.AsyncAppender=acs::AcsAsyncAppender
log4cplus.appender.AsyncAppender.QueueSize=8192
log4cplus.appender.AsyncAppender.FlushIntervalMs=100
log4cplus.appender.AsyncAppender.Appender=FileAppender

log4cplus.appender.FileAppender=log4cplus::FileAppender
log4cplus.appender.FileAppender.File=app.log
log4cplus.appender.FileAppender.layout=log4cplus::PatternLayout
log4cplus.appender.FileAppender.layout.ConversionPattern=%D{%Y-%m-%d %H:%M:%S.%q} [%t] %-5p %c - %m%n
```

## 配置参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| QueueSize | size_t | 8192 | 队列大小，必须是2的幂次方 |
| FlushIntervalMs | int | 100 | 刷新间隔（毫秒） |
| Appender | string | - | 后端appender配置 |

## 性能特点

### 基准测试结果

在典型的测试环境下（4核CPU，SSD硬盘）：

- **吞吐量**: 可达到100万条日志/秒以上
- **延迟**: 单次日志记录延迟通常在1-10微秒
- **内存使用**: 队列大小 × 事件大小（约200-500字节/事件）

### 性能优化技术

1. **无锁算法**: 避免互斥锁的开销和竞争
2. **批量处理**: 工作线程批量处理事件，减少系统调用
3. **内存对齐**: 避免false sharing，提高缓存效率
4. **环形缓冲区**: 减少内存分配和释放

## 监控和统计

AcsAsyncAppender提供了丰富的统计信息：

```cpp
// 获取统计信息
uint64_t processed = async_appender->getProcessedCount();
uint64_t dropped = async_appender->getDroppedCount();
size_t queue_size = async_appender->getCurrentQueueSize();

std::cout << "Processed: " << processed << std::endl;
std::cout << "Dropped: " << dropped << std::endl;
std::cout << "Queue size: " << queue_size << std::endl;
```

## 注意事项

1. **队列大小**: 队列大小必须是2的幂次方，建议根据应用的日志量来设置
2. **内存使用**: 队列会预分配内存，较大的队列会占用更多内存
3. **关闭处理**: 应用退出前应确保AcsAsyncAppender正确关闭，避免日志丢失
4. **异常处理**: 队列满时会丢弃日志事件，可通过统计信息监控

## 许可证

本项目采用Apache 2.0许可证，详见LICENSE文件。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 相关项目

- [log4cplus](https://github.com/log4cplus/log4cplus) - C++日志库
- [spdlog](https://github.com/gabime/spdlog) - 快速C++日志库
