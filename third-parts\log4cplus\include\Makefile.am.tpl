[= AutoGen5 template -*- Mode: scheme -*-
am
=][=
(use-modules (ice-9 ftw))
=]## Generated by Autogen from [= (tpl-file) =]
log4cplusincdir = $(includedir)

nobase_log4cplusinc_HEADERS = \
[= FOR src-dirs =][=
(let ((files (list)))
  (define (emit-am-file-ftw-cb filename statinfo flag)
    (begin
      (if (or (string-suffix-ci? ".h" filename)
              (string-suffix-ci? ".hxx" filename))
          (set! files (append! files (list filename))))
      #t))
  (begin
    (ftw (get "name") emit-am-file-ftw-cb)
    ;; Add the generated header as it will not be found by file search.
    (append! files (list "log4cplus/config/defines.hxx"))
    (set! files (sort! files string-ci<?))
    (emit "\t"
          (join " \\\n\t" files)
          "\n")))
=][= ENDFOR =]
