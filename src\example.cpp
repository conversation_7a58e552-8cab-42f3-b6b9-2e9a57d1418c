#include <iostream>
#include <chrono>
#include <thread>
#include <vector>
#include <random>

#include <log4cplus/logger.h>
#include <log4cplus/configurator.h>
#include <log4cplus/consoleappender.h>
#include <log4cplus/fileappender.h>
#include <log4cplus/layout.h>
#include <log4cplus/loggingmacros.h>

#include "acs_async_appender.h"

using namespace log4cplus;
using namespace acs;

/**
 * @brief 基本使用示例
 */
void basicUsageExample() {
    std::cout << "=== Basic Usage Example ===" << std::endl;
    
    try {
        // 创建一个文件appender作为后端
        auto file_appender = std::make_shared<FileAppender>(
            LOG4CPLUS_TEXT("test_async.log"), 
            std::ios_base::app);
        
        // 设置布局
        std::unique_ptr<Layout> layout(new PatternLayout(
            LOG4CPLUS_TEXT("%D{%Y-%m-%d %H:%M:%S.%q} [%t] %-5p %c - %m%n")));
        file_appender->setLayout(std::move(layout));
        
        // 创建AcsAsyncAppender
        auto async_appender = std::make_shared<AcsAsyncAppender>(
            file_appender, 
            4096,  // 队列大小
            50     // 刷新间隔50ms
        );
        async_appender->setName(LOG4CPLUS_TEXT("AsyncAppender"));
        
        // 获取logger并添加appender
        Logger logger = Logger::getInstance(LOG4CPLUS_TEXT("example"));
        logger.addAppender(async_appender);
        logger.setLogLevel(DEBUG_LOG_LEVEL);
        
        // 记录一些日志
        LOG4CPLUS_INFO(logger, "AcsAsyncAppender basic usage example started");
        LOG4CPLUS_DEBUG(logger, "This is a debug message");
        LOG4CPLUS_WARN(logger, "This is a warning message");
        LOG4CPLUS_ERROR(logger, "This is an error message");
        
        // 等待一段时间确保日志被处理
        std::this_thread::sleep_for(std::chrono::milliseconds(200));
        
        // 输出统计信息
        std::cout << "Processed events: " << async_appender->getProcessedCount() << std::endl;
        std::cout << "Dropped events: " << async_appender->getDroppedCount() << std::endl;
        std::cout << "Current queue size: " << async_appender->getCurrentQueueSize() << std::endl;
        
        LOG4CPLUS_INFO(logger, "Basic usage example completed");
        
    } catch (const std::exception& e) {
        std::cerr << "Error in basic usage example: " << e.what() << std::endl;
    }
}

/**
 * @brief 性能测试示例
 */
void performanceTest() {
    std::cout << "\n=== Performance Test ===" << std::endl;
    
    try {
        // 创建控制台appender作为后端（避免磁盘I/O影响测试）
        auto console_appender = std::make_shared<ConsoleAppender>();
        
        // 设置简单布局
        std::unique_ptr<Layout> layout(new PatternLayout(
            LOG4CPLUS_TEXT("%m%n")));
        console_appender->setLayout(std::move(layout));
        
        // 创建AcsAsyncAppender
        auto async_appender = std::make_shared<AcsAsyncAppender>(
            console_appender, 
            8192,  // 较大的队列
            100    // 刷新间隔100ms
        );
        
        // 获取logger
        Logger logger = Logger::getInstance(LOG4CPLUS_TEXT("perf_test"));
        logger.addAppender(async_appender);
        logger.setLogLevel(INFO_LOG_LEVEL);
        
        const int num_threads = 4;
        const int messages_per_thread = 10000;
        const int total_messages = num_threads * messages_per_thread;
        
        std::cout << "Starting performance test with " << num_threads 
                  << " threads, " << messages_per_thread 
                  << " messages per thread..." << std::endl;
        
        auto start_time = std::chrono::high_resolution_clock::now();
        
        // 创建多个线程并发写入日志
        std::vector<std::thread> threads;
        for (int i = 0; i < num_threads; ++i) {
            threads.emplace_back([&logger, messages_per_thread, i]() {
                std::random_device rd;
                std::mt19937 gen(rd());
                std::uniform_int_distribution<> dis(1, 1000);
                
                for (int j = 0; j < messages_per_thread; ++j) {
                    LOG4CPLUS_INFO(logger, "Thread " << i << " message " << j 
                                   << " random=" << dis(gen));
                }
            });
        }
        
        // 等待所有线程完成
        for (auto& t : threads) {
            t.join();
        }
        
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
            end_time - start_time);
        
        // 等待队列处理完成
        std::this_thread::sleep_for(std::chrono::milliseconds(500));
        
        // 输出性能统计
        double messages_per_second = static_cast<double>(total_messages) / 
                                   (duration.count() / 1000.0);
        
        std::cout << "Performance test completed:" << std::endl;
        std::cout << "  Total messages: " << total_messages << std::endl;
        std::cout << "  Duration: " << duration.count() << " ms" << std::endl;
        std::cout << "  Messages/second: " << static_cast<int>(messages_per_second) << std::endl;
        std::cout << "  Processed events: " << async_appender->getProcessedCount() << std::endl;
        std::cout << "  Dropped events: " << async_appender->getDroppedCount() << std::endl;
        std::cout << "  Final queue size: " << async_appender->getCurrentQueueSize() << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Error in performance test: " << e.what() << std::endl;
    }
}

/**
 * @brief 队列溢出测试
 */
void queueOverflowTest() {
    std::cout << "\n=== Queue Overflow Test ===" << std::endl;
    
    try {
        // 创建一个慢速的文件appender
        auto file_appender = std::make_shared<FileAppender>(
            LOG4CPLUS_TEXT("overflow_test.log"));
        
        // 创建小队列的AcsAsyncAppender
        auto async_appender = std::make_shared<AcsAsyncAppender>(
            file_appender, 
            64,    // 很小的队列
            1000   // 较长的刷新间隔
        );
        
        Logger logger = Logger::getInstance(LOG4CPLUS_TEXT("overflow_test"));
        logger.addAppender(async_appender);
        logger.setLogLevel(INFO_LOG_LEVEL);
        
        std::cout << "Sending many messages to small queue..." << std::endl;
        
        // 快速发送大量消息
        for (int i = 0; i < 1000; ++i) {
            LOG4CPLUS_INFO(logger, "Overflow test message " << i);
        }
        
        // 等待处理
        std::this_thread::sleep_for(std::chrono::milliseconds(200));
        
        std::cout << "Queue overflow test results:" << std::endl;
        std::cout << "  Processed events: " << async_appender->getProcessedCount() << std::endl;
        std::cout << "  Dropped events: " << async_appender->getDroppedCount() << std::endl;
        std::cout << "  Current queue size: " << async_appender->getCurrentQueueSize() << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Error in queue overflow test: " << e.what() << std::endl;
    }
}

int main() {
    try {
        // 初始化log4cplus
        log4cplus::initialize();
        
        std::cout << "AcsAsyncAppender Example and Test Program" << std::endl;
        std::cout << "=========================================" << std::endl;
        
        // 运行示例和测试
        basicUsageExample();
        performanceTest();
        queueOverflowTest();
        
        std::cout << "\nAll tests completed successfully!" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Fatal error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
