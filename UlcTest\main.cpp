#include <iostream>
#include <thread>
#include <vector>
#include <chrono>
#include <atomic>
#include <string>
#include <cstdlib>
#include "ulc_lockfree_queue.h"

using namespace ulc;
using namespace std;

bool testBasicFunctionality() {
    cout << "=== Basic Functionality Test ===" << endl;
    
    lockfree_queue<int> queue(16);
    
    // Test enqueue
    for (int i = 0; i < 10; ++i) {
        if (!queue.enqueue(i)) {
            cout << "Enqueue failed" << endl;
            return false;
        }
    }
    
    cout << "Enqueue test passed" << endl;
    
    // Test queue size
    if (queue.size() != 10) {
        cout << "Queue size error" << endl;
        return false;
    }
    cout << "Queue size test passed" << endl;
    
    // Test dequeue
    int value;
    for (int i = 0; i < 10; ++i) {
        if (!queue.dequeue(value) || value != i) {
            cout << "Dequeue test failed" << endl;
            return false;
        }
    }
    
    cout << "Dequeue test passed" << endl;
    
    // Test empty queue
    if (!queue.empty() || queue.dequeue(value)) {
        cout << "Empty queue test failed" << endl;
        return false;
    }
    cout << "Empty queue test passed" << endl;
    
    cout << "Basic functionality test completed!" << endl << endl;
    return true;
}

bool testBulkOperations() {
    cout << "=== Bulk Operations Test ===" << endl;
    
    lockfree_queue<string> queue(64);
    
    // Fill queue
    vector<string> test_data = {
        "Hello", "World", "Lock", "Free", "Queue", "Test", "Bulk", "Operations"
    };
    
    for (const auto& str : test_data) {
        if (!queue.enqueue(str)) {
            cout << "Bulk test enqueue failed" << endl;
            return false;
        }
    }
    
    // Bulk dequeue
    vector<string> result;
    size_t count = queue.dequeue_bulk(result, 5);
    
    if (count != 5 || result.size() != 5) {
        cout << "Bulk dequeue count error" << endl;
        return false;
    }
    
    for (size_t i = 0; i < count; ++i) {
        if (result[i] != test_data[i]) {
            cout << "Bulk dequeue content error" << endl;
            return false;
        }
    }
    
    cout << "Bulk dequeue test passed" << endl;
    
    // Dequeue remaining elements
    count = queue.dequeue_bulk(result, 10);
    if (count != 3 || result.size() != 3) {
        cout << "Remaining elements dequeue error" << endl;
        return false;
    }
    
    cout << "Bulk operations test completed!" << endl << endl;
    return true;
}

bool testMultiThreaded() {
    cout << "=== Multi-threaded Test ===" << endl;
    
    const size_t num_producers = 2;
    const size_t items_per_producer = 1000;
    const size_t total_items = num_producers * items_per_producer;
    
    lockfree_queue<int> queue(4096);
    atomic<size_t> produced_count(0);
    atomic<size_t> consumed_count(0);
    atomic<bool> producers_done(false);
    
    // Start producer threads
    vector<thread> producers;
    for (size_t i = 0; i < num_producers; ++i) {
        producers.emplace_back([&, i]() {
            for (size_t j = 0; j < items_per_producer; ++j) {
                int value = static_cast<int>(i * items_per_producer + j);
                while (!queue.enqueue(value)) {
                    this_thread::yield();
                }
                produced_count.fetch_add(1, memory_order_relaxed);
            }
        });
    }
    
    // Start consumer thread
    thread consumer([&]() {
        vector<int> items;
        while (!producers_done.load() || !queue.empty()) {
            size_t count = queue.dequeue_bulk(items, 32);
            if (count > 0) {
                consumed_count.fetch_add(count, memory_order_relaxed);
            } else {
                this_thread::yield();
            }
        }
    });
    
    // Wait for producers to complete
    for (auto& t : producers) {
        t.join();
    }
    producers_done.store(true);
    
    // Wait for consumer to complete
    consumer.join();
    
    cout << "Produced " << produced_count.load() << " elements" << endl;
    cout << "Consumed " << consumed_count.load() << " elements" << endl;
    
    if (produced_count.load() != total_items || consumed_count.load() != total_items) {
        cout << "Multi-threaded test failed" << endl;
        return false;
    }
    
    cout << "Multi-threaded test passed!" << endl << endl;
    return true;
}

void performanceBenchmark() {
    cout << "=== Performance Benchmark ===" << endl;
    
    const size_t num_operations = 100000;
    lockfree_queue<int> queue(16384);
    
    auto start = chrono::high_resolution_clock::now();
    
    // Enqueue test
    for (size_t i = 0; i < num_operations; ++i) {
        while (!queue.enqueue(static_cast<int>(i))) {
            // Queue full, wait
        }
    }
    
    // Dequeue test
    int value;
    for (size_t i = 0; i < num_operations; ++i) {
        while (!queue.dequeue(value)) {
            // Queue empty, wait
        }
    }
    
    auto end = chrono::high_resolution_clock::now();
    auto duration = chrono::duration_cast<chrono::microseconds>(end - start);
    
    size_t total_operations = num_operations * 2; // enqueue + dequeue
    double duration_ms = duration.count() / 1000.0;
    double ops_per_second = total_operations / (duration_ms / 1000.0);
    
    cout << "Operations: " << total_operations << endl;
    cout << "Duration: " << duration_ms << " ms" << endl;
    cout << "Performance: " << static_cast<int>(ops_per_second) << " ops/sec" << endl;
    cout << endl;
}

int main() {
    cout << "ULC Lock-Free Queue Test Program" << endl;
    cout << "=================================" << endl << endl;
    
    try {
        // Basic functionality test
        if (!testBasicFunctionality()) {
            cout << "Basic functionality test failed!" << endl;
            system("pause");
            return 1;
        }
        
        // Bulk operations test
        if (!testBulkOperations()) {
            cout << "Bulk operations test failed!" << endl;
            system("pause");
            return 1;
        }
        
        // Multi-threaded test
        if (!testMultiThreaded()) {
            cout << "Multi-threaded test failed!" << endl;
            system("pause");
            return 1;
        }
        
        // Performance benchmark
        performanceBenchmark();
        
        cout << "All tests passed! Lock-free queue performance meets spdlog requirements!" << endl;
        
    } catch (const exception& e) {
        cout << "Exception occurred during testing: " << e.what() << endl;
        system("pause");
        return 1;
    }
    
    system("pause");
    return 0;
}
