#include <iostream>
#include <chrono>
#include <thread>
#include <vector>
#include <atomic>
#include <iomanip>

#include <log4cplus/logger.h>
#include <log4cplus/fileappender.h>
#include <log4cplus/nullappender.h>
#include <log4cplus/layout.h>
#include <log4cplus/loggingmacros.h>

#include "acs_async_appender.h"

using namespace log4cplus;
using namespace acs;

class BenchmarkTimer {
public:
    BenchmarkTimer() : start_(std::chrono::high_resolution_clock::now()) {}
    
    double elapsed_ms() const {
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start_);
        return duration.count() / 1000.0;
    }
    
    void reset() {
        start_ = std::chrono::high_resolution_clock::now();
    }

private:
    std::chrono::high_resolution_clock::time_point start_;
};

struct BenchmarkResult {
    int threads;
    int messages_per_thread;
    double duration_ms;
    uint64_t processed_count;
    uint64_t dropped_count;
    
    double messages_per_second() const {
        return (threads * messages_per_thread) / (duration_ms / 1000.0);
    }
    
    double avg_latency_us() const {
        return (duration_ms * 1000.0) / (threads * messages_per_thread);
    }
};

void printResult(const std::string& test_name, const BenchmarkResult& result) {
    std::cout << "\n" << test_name << " Results:" << std::endl;
    std::cout << "  Threads: " << result.threads << std::endl;
    std::cout << "  Messages per thread: " << result.messages_per_thread << std::endl;
    std::cout << "  Total messages: " << (result.threads * result.messages_per_thread) << std::endl;
    std::cout << "  Duration: " << std::fixed << std::setprecision(2) << result.duration_ms << " ms" << std::endl;
    std::cout << "  Messages/second: " << std::fixed << std::setprecision(0) << result.messages_per_second() << std::endl;
    std::cout << "  Average latency: " << std::fixed << std::setprecision(2) << result.avg_latency_us() << " μs" << std::endl;
    std::cout << "  Processed: " << result.processed_count << std::endl;
    std::cout << "  Dropped: " << result.dropped_count << std::endl;
}

BenchmarkResult runAsyncBenchmark(int num_threads, int messages_per_thread, bool use_file = false) {
    // 创建后端appender
    SharedAppenderPtr backend_appender;
    if (use_file) {
        backend_appender = std::make_shared<FileAppender>(
            LOG4CPLUS_TEXT("benchmark.log"), std::ios_base::trunc);
    } else {
        backend_appender = std::make_shared<NullAppender>();
    }
    
    // 设置简单布局
    std::unique_ptr<Layout> layout(new PatternLayout(
        LOG4CPLUS_TEXT("%D{%H:%M:%S.%q} [%t] %-5p - %m%n")));
    backend_appender->setLayout(std::move(layout));
    
    // 创建异步appender
    auto async_appender = std::make_shared<AcsAsyncAppender>(
        backend_appender, 
        16384,  // 大队列
        50      // 50ms刷新间隔
    );
    
    // 创建logger
    Logger logger = Logger::getInstance(LOG4CPLUS_TEXT("benchmark"));
    logger.removeAllAppenders();
    logger.addAppender(async_appender);
    logger.setLogLevel(INFO_LOG_LEVEL);
    
    // 准备测试数据
    std::atomic<bool> start_flag(false);
    std::vector<std::thread> threads;
    
    BenchmarkTimer timer;
    
    // 创建线程
    for (int i = 0; i < num_threads; ++i) {
        threads.emplace_back([&, i]() {
            // 等待开始信号
            while (!start_flag.load()) {
                std::this_thread::yield();
            }
            
            // 执行日志记录
            for (int j = 0; j < messages_per_thread; ++j) {
                LOG4CPLUS_INFO(logger, "Benchmark message from thread " << i 
                               << " message " << j << " with some additional data");
            }
        });
    }
    
    // 开始测试
    timer.reset();
    start_flag.store(true);
    
    // 等待所有线程完成
    for (auto& t : threads) {
        t.join();
    }
    
    double duration = timer.elapsed_ms();
    
    // 等待队列处理完成
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
    
    BenchmarkResult result;
    result.threads = num_threads;
    result.messages_per_thread = messages_per_thread;
    result.duration_ms = duration;
    result.processed_count = async_appender->getProcessedCount();
    result.dropped_count = async_appender->getDroppedCount();
    
    return result;
}

BenchmarkResult runSyncBenchmark(int num_threads, int messages_per_thread, bool use_file = false) {
    // 创建同步appender
    SharedAppenderPtr appender;
    if (use_file) {
        appender = std::make_shared<FileAppender>(
            LOG4CPLUS_TEXT("benchmark_sync.log"), std::ios_base::trunc);
    } else {
        appender = std::make_shared<NullAppender>();
    }
    
    // 设置简单布局
    std::unique_ptr<Layout> layout(new PatternLayout(
        LOG4CPLUS_TEXT("%D{%H:%M:%S.%q} [%t] %-5p - %m%n")));
    appender->setLayout(std::move(layout));
    
    // 创建logger
    Logger logger = Logger::getInstance(LOG4CPLUS_TEXT("benchmark_sync"));
    logger.removeAllAppenders();
    logger.addAppender(appender);
    logger.setLogLevel(INFO_LOG_LEVEL);
    
    // 准备测试数据
    std::atomic<bool> start_flag(false);
    std::vector<std::thread> threads;
    
    BenchmarkTimer timer;
    
    // 创建线程
    for (int i = 0; i < num_threads; ++i) {
        threads.emplace_back([&, i]() {
            // 等待开始信号
            while (!start_flag.load()) {
                std::this_thread::yield();
            }
            
            // 执行日志记录
            for (int j = 0; j < messages_per_thread; ++j) {
                LOG4CPLUS_INFO(logger, "Benchmark message from thread " << i 
                               << " message " << j << " with some additional data");
            }
        });
    }
    
    // 开始测试
    timer.reset();
    start_flag.store(true);
    
    // 等待所有线程完成
    for (auto& t : threads) {
        t.join();
    }
    
    double duration = timer.elapsed_ms();
    
    BenchmarkResult result;
    result.threads = num_threads;
    result.messages_per_thread = messages_per_thread;
    result.duration_ms = duration;
    result.processed_count = num_threads * messages_per_thread;
    result.dropped_count = 0;
    
    return result;
}

void runBenchmarkSuite() {
    std::cout << "AcsAsyncAppender Benchmark Suite" << std::endl;
    std::cout << "=================================" << std::endl;
    
    // 测试配置
    std::vector<std::pair<int, int>> test_configs = {
        {1, 100000},   // 单线程，10万条消息
        {2, 50000},    // 2线程，每线程5万条
        {4, 25000},    // 4线程，每线程2.5万条
        {8, 12500},    // 8线程，每线程1.25万条
    };
    
    for (const auto& config : test_configs) {
        int threads = config.first;
        int messages = config.second;
        
        std::cout << "\n--- Testing with " << threads << " threads, " 
                  << messages << " messages per thread ---" << std::endl;
        
        // 异步测试（内存）
        auto async_result = runAsyncBenchmark(threads, messages, false);
        printResult("AcsAsyncAppender (Memory)", async_result);
        
        // 同步测试（内存）
        auto sync_result = runSyncBenchmark(threads, messages, false);
        printResult("Synchronous Appender (Memory)", sync_result);
        
        // 性能提升比较
        double speedup = async_result.messages_per_second() / sync_result.messages_per_second();
        std::cout << "  Speedup: " << std::fixed << std::setprecision(2) << speedup << "x" << std::endl;
    }
    
    // 文件I/O测试
    std::cout << "\n--- File I/O Performance Test ---" << std::endl;
    auto async_file_result = runAsyncBenchmark(4, 10000, true);
    printResult("AcsAsyncAppender (File)", async_file_result);
    
    auto sync_file_result = runSyncBenchmark(4, 10000, true);
    printResult("Synchronous Appender (File)", sync_file_result);
    
    double file_speedup = async_file_result.messages_per_second() / sync_file_result.messages_per_second();
    std::cout << "  File I/O Speedup: " << std::fixed << std::setprecision(2) << file_speedup << "x" << std::endl;
}

int main() {
    try {
        log4cplus::initialize();
        runBenchmarkSuite();
        std::cout << "\nBenchmark completed successfully!" << std::endl;
    } catch (const std::exception& e) {
        std::cerr << "Benchmark failed: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
