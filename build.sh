#!/bin/bash

echo "Building AcsAsyncAppender..."

# 创建构建目录
mkdir -p build
cd build

# 配置项目
echo "Configuring project..."
cmake .. -DCMAKE_BUILD_TYPE=Release
if [ $? -ne 0 ]; then
    echo "Failed to configure project"
    exit 1
fi

# 编译项目
echo "Building project..."
cmake --build . -j$(nproc)
if [ $? -ne 0 ]; then
    echo "Failed to build project"
    exit 1
fi

echo "Build completed successfully!"
echo
echo "Executables are located in:"
echo "  bin/example"
echo "  bin/benchmark"
echo
echo "To run the example:"
echo "  cd build"
echo "  ./bin/example"
echo
echo "To run the benchmark:"
echo "  cd build"
echo "  ./bin/benchmark"
