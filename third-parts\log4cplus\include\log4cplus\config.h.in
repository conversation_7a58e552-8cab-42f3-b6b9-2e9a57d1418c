/* include/log4cplus/config.h.in.  Generated from configure.ac by autoheader.  */

#ifndef LOG4CPLUS_CONFIG_H

#define LOG4CPLUS_CONFIG_H

/* define if the compiler supports basic C++11 syntax */
#undef HAVE_CXX11

/* Define to 1 if you have the <dlfcn.h> header file. */
#undef HAVE_DLFCN_H

/* Define to 1 if you have the `fcntl' function. */
#undef HAVE_FCNTL

/* Define to 1 if you have the `flock' function. */
#undef HAVE_FLOCK

/* Define to 1 if you have the `ftime' function. */
#undef HAVE_FTIME

/* Define to 1 if the system has the `constructor' function attribute */
#undef HAVE_FUNC_ATTRIBUTE_CONSTRUCTOR

/* Define to 1 if the system has the `constructor_priority' function attribute
   */
#undef HAVE_FUNC_ATTRIBUTE_CONSTRUCTOR_PRIORITY

/* */
#undef HAVE_GETADDRINFO

/* */
#undef HAVE_GETHOSTBYNAME_R

/* Define to 1 if you have the `getpid' function. */
#undef HAVE_GETPID

/* Define to 1 if you have the `gmtime_r' function. */
#undef HAVE_GMTIME_R

/* Define to 1 if you have the `htonl' function. */
#undef HAVE_HTONL

/* Define to 1 if you have the `htons' function. */
#undef HAVE_HTONS

/* Define to 1 if you have the `iconv' function. */
#undef HAVE_ICONV

/* Define to 1 if you have the `iconv_close' function. */
#undef HAVE_ICONV_CLOSE

/* Define to 1 if you have the `iconv_open' function. */
#undef HAVE_ICONV_OPEN

/* Define to 1 if you have the <inttypes.h> header file. */
#undef HAVE_INTTYPES_H

/* Define to 1 if you have the `advapi32' library (-ladvapi32). */
#undef HAVE_LIBADVAPI32

/* Define to 1 if you have the `libiconv' function. */
#undef HAVE_LIBICONV

/* Define to 1 if you have the `libiconv_close' function. */
#undef HAVE_LIBICONV_CLOSE

/* Define to 1 if you have the `libiconv_open' function. */
#undef HAVE_LIBICONV_OPEN

/* Define to 1 if you have the `kernel32' library (-lkernel32). */
#undef HAVE_LIBKERNEL32

/* Define to 1 if you have the `oleaut32' library (-loleaut32). */
#undef HAVE_LIBOLEAUT32

/* Define to 1 if you have the `ws2_32' library (-lws2_32). */
#undef HAVE_LIBWS2_32

/* Define to 1 if you have the `localtime_r' function. */
#undef HAVE_LOCALTIME_R

/* Define to 1 if you have the `lockf' function. */
#undef HAVE_LOCKF

/* Define to 1 if you have the `lstat' function. */
#undef HAVE_LSTAT

/* Define to 1 if you have the `mbstowcs' function. */
#undef HAVE_MBSTOWCS

/* Define to 1 if you have the <memory.h> header file. */
#undef HAVE_MEMORY_H

/* Define to 1 if you have the `ntohl' function. */
#undef HAVE_NTOHL

/* Define to 1 if you have the `ntohs' function. */
#undef HAVE_NTOHS

/* Define to 1 if you have the `OutputDebugStringW' function. */
#undef HAVE_OUTPUTDEBUGSTRINGW

/* Define to 1 if you have the `pipe' function. */
#undef HAVE_PIPE

/* Define to 1 if you have the `pipe2' function. */
#undef HAVE_PIPE2

/* Define to 1 if you have the `poll' function. */
#undef HAVE_POLL

/* Define if you have POSIX threads libraries and header files. */
#undef HAVE_PTHREAD

/* Have PTHREAD_PRIO_INHERIT. */
#undef HAVE_PTHREAD_PRIO_INHERIT

/* If available, contains the Python version number currently in use. */
#undef HAVE_PYTHON

/* Define to 1 if you have the `shutdown' function. */
#undef HAVE_SHUTDOWN

/* Define to 1 if you have the `stat' function. */
#undef HAVE_STAT

/* Define to 1 if you have the <stdint.h> header file. */
#undef HAVE_STDINT_H

/* Define to 1 if you have the <stdlib.h> header file. */
#undef HAVE_STDLIB_H

/* Define to 1 if you have the <strings.h> header file. */
#undef HAVE_STRINGS_H

/* Define to 1 if you have the <string.h> header file. */
#undef HAVE_STRING_H

/* Define to 1 if you have the <sys/stat.h> header file. */
#undef HAVE_SYS_STAT_H

/* Define to 1 if you have the <sys/types.h> header file. */
#undef HAVE_SYS_TYPES_H

/* Defined if the compiler understands __thread or __declspec(thread)
   construct. */
#undef HAVE_TLS_SUPPORT

/* Define to 1 if you have the <unistd.h> header file. */
#undef HAVE_UNISTD_H

/* Define to 1 if the system has the `init_priority' variable attribute */
#undef HAVE_VAR_ATTRIBUTE_INIT_PRIORITY

/* Define to 1 if you have the `vfprintf_s' function. */
#undef HAVE_VFPRINTF_S

/* Define to 1 if you have the `vfwprintf_s' function. */
#undef HAVE_VFWPRINTF_S

/* Define to 1 if you have the `vsnprintf' function. */
#undef HAVE_VSNPRINTF

/* Define to 1 if you have the `vsnwprintf' function. */
#undef HAVE_VSNWPRINTF

/* Define to 1 if you have the `vsprintf_s' function. */
#undef HAVE_VSPRINTF_S

/* Define to 1 if you have the `vswprintf_s' function. */
#undef HAVE_VSWPRINTF_S

/* Define to 1 if you have the `wcstombs' function. */
#undef HAVE_WCSTOMBS

/* Define to 1 if you have the `_vsnprintf' function. */
#undef HAVE__VSNPRINTF

/* Define to 1 if you have the `_vsnprintf_s' function. */
#undef HAVE__VSNPRINTF_S

/* Define to 1 if you have the `_vsnwprintf' function. */
#undef HAVE__VSNWPRINTF

/* Define to 1 if you have the `_vsnwprintf_s' function. */
#undef HAVE__VSNWPRINTF_S

/* Defined if the compiler supports __FUNCTION__ macro. */
#undef HAVE___FUNCTION___MACRO

/* Defined if the compiler supports __func__ symbol. */
#undef HAVE___FUNC___SYMBOL

/* Defined if the compiler supports __PRETTY_FUNCTION__ macro. */
#undef HAVE___PRETTY_FUNCTION___MACRO

/* Defined for --enable-debugging builds. */
#undef LOG4CPLUS_DEBUGGING

/* Defined if the compiler understands __declspec(dllimport) or
   __attribute__((visibility("default"))) or __global construct. */
#undef LOG4CPLUS_DECLSPEC_EXPORT

/* Defined if the compiler understands __declspec(dllimport) or
   __attribute__((visibility("default"))) or __global construct. */
#undef LOG4CPLUS_DECLSPEC_IMPORT

/* Defined if the compiler understands __attribute__((visibility("hidden")))
   or __hidden construct. */
#undef LOG4CPLUS_DECLSPEC_PRIVATE

/* */
#undef LOG4CPLUS_HAVE_ARPA_INET_H

/* */
#undef LOG4CPLUS_HAVE_ENAMETOOLONG

/* */
#undef LOG4CPLUS_HAVE_ERRNO_H

/* */
#undef LOG4CPLUS_HAVE_FCNTL

/* */
#undef LOG4CPLUS_HAVE_FCNTL_H

/* */
#undef LOG4CPLUS_HAVE_FLOCK

/* */
#undef LOG4CPLUS_HAVE_FTIME

/* */
#undef LOG4CPLUS_HAVE_FUNCTION_MACRO

/* */
#undef LOG4CPLUS_HAVE_FUNC_ATTRIBUTE_CONSTRUCTOR

/* */
#undef LOG4CPLUS_HAVE_FUNC_ATTRIBUTE_CONSTRUCTOR_PRIORITY

/* */
#undef LOG4CPLUS_HAVE_FUNC_SYMBOL

/* */
#undef LOG4CPLUS_HAVE_GETADDRINFO

/* */
#undef LOG4CPLUS_HAVE_GETHOSTBYNAME_R

/* */
#undef LOG4CPLUS_HAVE_GETPID

/* */
#undef LOG4CPLUS_HAVE_GETTID

/* */
#undef LOG4CPLUS_HAVE_GMTIME_R

/* */
#undef LOG4CPLUS_HAVE_HTONL

/* */
#undef LOG4CPLUS_HAVE_HTONS

/* */
#undef LOG4CPLUS_HAVE_ICONV

/* */
#undef LOG4CPLUS_HAVE_ICONV_CLOSE

/* */
#undef LOG4CPLUS_HAVE_ICONV_H

/* */
#undef LOG4CPLUS_HAVE_ICONV_OPEN

/* */
#undef LOG4CPLUS_HAVE_LIMITS_H

/* */
#undef LOG4CPLUS_HAVE_LOCALTIME_R

/* */
#undef LOG4CPLUS_HAVE_LOCKF

/* */
#undef LOG4CPLUS_HAVE_LSTAT

/* */
#undef LOG4CPLUS_HAVE_MBSTOWCS

/* */
#undef LOG4CPLUS_HAVE_NETDB_H

/* */
#undef LOG4CPLUS_HAVE_NETINET_IN_H

/* */
#undef LOG4CPLUS_HAVE_NETINET_TCP_H

/* */
#undef LOG4CPLUS_HAVE_NTOHL

/* */
#undef LOG4CPLUS_HAVE_NTOHS

/* */
#undef LOG4CPLUS_HAVE_OUTPUTDEBUGSTRING

/* */
#undef LOG4CPLUS_HAVE_PIPE

/* */
#undef LOG4CPLUS_HAVE_PIPE2

/* */
#undef LOG4CPLUS_HAVE_POLL

/* */
#undef LOG4CPLUS_HAVE_POLL_H

/* */
#undef LOG4CPLUS_HAVE_PRETTY_FUNCTION_MACRO

/* */
#undef LOG4CPLUS_HAVE_SHUTDOWN

/* */
#undef LOG4CPLUS_HAVE_STAT

/* */
#undef LOG4CPLUS_HAVE_STDARG_H

/* */
#undef LOG4CPLUS_HAVE_STDIO_H

/* */
#undef LOG4CPLUS_HAVE_STDLIB_H

/* */
#undef LOG4CPLUS_HAVE_SYSLOG_H

/* */
#undef LOG4CPLUS_HAVE_SYS_FILE_H

/* */
#undef LOG4CPLUS_HAVE_SYS_SOCKET_H

/* */
#undef LOG4CPLUS_HAVE_SYS_STAT_H

/* */
#undef LOG4CPLUS_HAVE_SYS_SYSCALL_H

/* */
#undef LOG4CPLUS_HAVE_SYS_TIMEB_H

/* */
#undef LOG4CPLUS_HAVE_SYS_TIME_H

/* */
#undef LOG4CPLUS_HAVE_SYS_TYPES_H

/* */
#undef LOG4CPLUS_HAVE_TIME_H

/* */
#undef LOG4CPLUS_HAVE_TLS_SUPPORT

/* */
#undef LOG4CPLUS_HAVE_UNISTD_H

/* */
#undef LOG4CPLUS_HAVE_VAR_ATTRIBUTE_INIT_PRIORITY

/* */
#undef LOG4CPLUS_HAVE_VFPRINTF_S

/* */
#undef LOG4CPLUS_HAVE_VFWPRINTF_S

/* */
#undef LOG4CPLUS_HAVE_VSNPRINTF

/* */
#undef LOG4CPLUS_HAVE_VSNWPRINTF

/* */
#undef LOG4CPLUS_HAVE_VSPRINTF_S

/* */
#undef LOG4CPLUS_HAVE_VSWPRINTF_S

/* */
#undef LOG4CPLUS_HAVE_WCHAR_H

/* */
#undef LOG4CPLUS_HAVE_WCSTOMBS

/* */
#undef LOG4CPLUS_HAVE__VSNPRINTF

/* */
#undef LOG4CPLUS_HAVE__VSNPRINTF_S

/* */
#undef LOG4CPLUS_HAVE__VSNWPRINTF

/* */
#undef LOG4CPLUS_HAVE__VSNWPRINTF_S

/* Define if this is a single-threaded library. */
#undef LOG4CPLUS_SINGLE_THREADED

/* */
#undef LOG4CPLUS_THREAD_LOCAL_VAR

/* */
#undef LOG4CPLUS_USE_PTHREADS

/* Define when iconv() is available. */
#undef LOG4CPLUS_WITH_ICONV

/* Defined to enable unit tests. */
#undef LOG4CPLUS_WITH_UNIT_TESTS

/* Define for C99 compilers/standard libraries that support more than just the
   "C" locale. */
#undef LOG4CPLUS_WORKING_C_LOCALE

/* Define for compilers/standard libraries that support more than just the "C"
   locale. */
#undef LOG4CPLUS_WORKING_LOCALE

/* Define to the sub-directory where libtool stores uninstalled libraries. */
#undef LT_OBJDIR

/* Define to the address where bug reports for this package should be sent. */
#undef PACKAGE_BUGREPORT

/* Define to the full name of this package. */
#undef PACKAGE_NAME

/* Define to the full name and version of this package. */
#undef PACKAGE_STRING

/* Define to the one symbol short name of this package. */
#undef PACKAGE_TARNAME

/* Define to the home page for this package. */
#undef PACKAGE_URL

/* Define to the version of this package. */
#undef PACKAGE_VERSION

/* Define to necessary symbol if this constant uses a non-standard name on
   your system. */
#undef PTHREAD_CREATE_JOINABLE

/* Define to 1 if you have the ANSI C header files. */
#undef STDC_HEADERS

/* Defined to the actual TLS support construct. */
#undef TLS_SUPPORT_CONSTRUCT

/* Substitute for socklen_t */
#undef socklen_t

#endif // LOG4CPLUS_CONFIG_H
