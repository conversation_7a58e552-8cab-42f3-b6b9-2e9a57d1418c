#pragma once

#include <atomic>
#include <memory>
#include <cstddef>
#include <log4cplus/spi/loggingevent.h>

namespace acs {

/**
 * @brief 高性能无锁环形缓冲区队列
 * 
 * 基于原子操作实现的无锁队列，支持多生产者单消费者模式(MPSC)
 * 使用环形缓冲区提高内存利用率和缓存友好性
 * 
 * 特性：
 * - 无锁设计，避免线程阻塞
 * - 环形缓冲区，内存使用效率高
 * - 支持多生产者单消费者
 * - 基于原子操作，保证线程安全
 */
template<typename T>
class LockFreeQueue {
public:
    /**
     * @brief 构造函数
     * @param capacity 队列容量，必须是2的幂次方
     */
    explicit LockFreeQueue(size_t capacity = 8192) 
        : capacity_(capacity)
        , mask_(capacity - 1)
        , buffer_(std::make_unique<Node[]>(capacity))
        , head_(0)
        , tail_(0) {
        
        // 确保容量是2的幂次方
        if ((capacity & (capacity - 1)) != 0) {
            throw std::invalid_argument("Capacity must be a power of 2");
        }
        
        // 初始化所有节点的序列号
        for (size_t i = 0; i < capacity; ++i) {
            buffer_[i].sequence.store(i, std::memory_order_relaxed);
        }
    }

    /**
     * @brief 析构函数
     */
    ~LockFreeQueue() = default;

    /**
     * @brief 尝试将元素入队
     * @param item 要入队的元素
     * @return true 成功入队，false 队列已满
     */
    bool try_enqueue(const T& item) {
        Node* node;
        size_t pos = tail_.load(std::memory_order_relaxed);
        
        for (;;) {
            node = &buffer_[pos & mask_];
            size_t seq = node->sequence.load(std::memory_order_acquire);
            intptr_t diff = (intptr_t)seq - (intptr_t)pos;
            
            if (diff == 0) {
                // 找到可用位置，尝试CAS更新tail
                if (tail_.compare_exchange_weak(pos, pos + 1, std::memory_order_relaxed)) {
                    break;
                }
            } else if (diff < 0) {
                // 队列已满
                return false;
            } else {
                // 其他线程已经更新了tail，重新获取
                pos = tail_.load(std::memory_order_relaxed);
            }
        }
        
        // 存储数据
        node->data = item;
        node->sequence.store(pos + 1, std::memory_order_release);
        return true;
    }

    /**
     * @brief 尝试将元素入队（移动语义）
     * @param item 要入队的元素
     * @return true 成功入队，false 队列已满
     */
    bool try_enqueue(T&& item) {
        Node* node;
        size_t pos = tail_.load(std::memory_order_relaxed);
        
        for (;;) {
            node = &buffer_[pos & mask_];
            size_t seq = node->sequence.load(std::memory_order_acquire);
            intptr_t diff = (intptr_t)seq - (intptr_t)pos;
            
            if (diff == 0) {
                if (tail_.compare_exchange_weak(pos, pos + 1, std::memory_order_relaxed)) {
                    break;
                }
            } else if (diff < 0) {
                return false;
            } else {
                pos = tail_.load(std::memory_order_relaxed);
            }
        }
        
        node->data = std::move(item);
        node->sequence.store(pos + 1, std::memory_order_release);
        return true;
    }

    /**
     * @brief 尝试从队列中取出元素
     * @param item 输出参数，存储取出的元素
     * @return true 成功取出，false 队列为空
     */
    bool try_dequeue(T& item) {
        Node* node;
        size_t pos = head_.load(std::memory_order_relaxed);
        
        for (;;) {
            node = &buffer_[pos & mask_];
            size_t seq = node->sequence.load(std::memory_order_acquire);
            intptr_t diff = (intptr_t)seq - (intptr_t)(pos + 1);
            
            if (diff == 0) {
                // 找到可读取的元素，尝试CAS更新head
                if (head_.compare_exchange_weak(pos, pos + 1, std::memory_order_relaxed)) {
                    break;
                }
            } else if (diff < 0) {
                // 队列为空
                return false;
            } else {
                // 重新获取head位置
                pos = head_.load(std::memory_order_relaxed);
            }
        }
        
        // 读取数据
        item = std::move(node->data);
        node->sequence.store(pos + mask_ + 1, std::memory_order_release);
        return true;
    }

    /**
     * @brief 获取队列当前大小（近似值）
     * @return 队列大小
     */
    size_t size() const {
        size_t tail = tail_.load(std::memory_order_acquire);
        size_t head = head_.load(std::memory_order_acquire);
        return tail - head;
    }

    /**
     * @brief 检查队列是否为空
     * @return true 队列为空，false 队列非空
     */
    bool empty() const {
        return size() == 0;
    }

    /**
     * @brief 获取队列容量
     * @return 队列容量
     */
    size_t capacity() const {
        return capacity_;
    }

private:
    struct Node {
        std::atomic<size_t> sequence;
        T data;
    };

    // 禁用拷贝和赋值
    LockFreeQueue(const LockFreeQueue&) = delete;
    LockFreeQueue& operator=(const LockFreeQueue&) = delete;

    const size_t capacity_;
    const size_t mask_;
    std::unique_ptr<Node[]> buffer_;
    
    // 使用缓存行对齐避免false sharing
    alignas(64) std::atomic<size_t> head_;
    alignas(64) std::atomic<size_t> tail_;
};

// 特化为log4cplus的日志事件类型
using LogEventQueue = LockFreeQueue<log4cplus::spi::InternalLoggingEvent>;

} // namespace acs
