#pragma once

#include <log4cplus/appender.h>
#include <log4cplus/helpers/appenderattachableimpl.h>
#include <log4cplus/helpers/property.h>
#include <thread>
#include <atomic>
#include <chrono>
#include <memory>
#include "lockfree_queue.h"

namespace acs {

/**
 * @brief 基于无锁队列的异步Appender
 * 
 * AcsAsyncAppender是一个高性能的异步日志appender，使用无锁队列来缓存日志事件，
 * 并通过专用的工作线程来处理实际的日志输出。这种设计可以显著减少日志记录对
 * 应用程序主线程的性能影响。
 * 
 * 特性：
 * - 基于无锁队列，避免线程阻塞
 * - 支持多个后端appender
 * - 可配置队列大小和刷新间隔
 * - 优雅的关闭机制
 * - 高性能，低延迟
 * 
 * 配置属性：
 * - QueueSize: 队列大小，默认8192，必须是2的幂次方
 * - FlushIntervalMs: 刷新间隔（毫秒），默认100ms
 * - Appender: 后端appender配置
 */
class AcsAsyncAppender 
    : public log4cplus::Appender
    , public log4cplus::helpers::AppenderAttachableImpl {
public:
    /**
     * @brief 构造函数
     * @param properties 配置属性
     */
    explicit AcsAsyncAppender(const log4cplus::helpers::Properties& properties);

    /**
     * @brief 构造函数
     * @param backend_appender 后端appender
     * @param queue_size 队列大小，默认8192
     * @param flush_interval_ms 刷新间隔（毫秒），默认100
     */
    explicit AcsAsyncAppender(
        log4cplus::SharedAppenderPtr backend_appender,
        size_t queue_size = 8192,
        int flush_interval_ms = 100);

    /**
     * @brief 析构函数
     */
    virtual ~AcsAsyncAppender();

    /**
     * @brief 关闭appender
     */
    virtual void close() override;

    /**
     * @brief 设置队列大小
     * @param size 队列大小，必须是2的幂次方
     */
    void setQueueSize(size_t size);

    /**
     * @brief 获取队列大小
     * @return 队列大小
     */
    size_t getQueueSize() const { return queue_size_; }

    /**
     * @brief 设置刷新间隔
     * @param interval_ms 刷新间隔（毫秒）
     */
    void setFlushInterval(int interval_ms);

    /**
     * @brief 获取刷新间隔
     * @return 刷新间隔（毫秒）
     */
    int getFlushInterval() const { return flush_interval_ms_; }

    /**
     * @brief 获取队列当前大小
     * @return 队列当前大小
     */
    size_t getCurrentQueueSize() const;

    /**
     * @brief 获取丢弃的日志事件数量
     * @return 丢弃的事件数量
     */
    uint64_t getDroppedCount() const { return dropped_count_.load(); }

    /**
     * @brief 获取处理的日志事件数量
     * @return 处理的事件数量
     */
    uint64_t getProcessedCount() const { return processed_count_.load(); }

protected:
    /**
     * @brief 追加日志事件
     * @param event 日志事件
     */
    virtual void append(const log4cplus::spi::InternalLoggingEvent& event) override;

private:
    /**
     * @brief 初始化appender
     */
    void init();

    /**
     * @brief 工作线程函数
     */
    void workerThread();

    /**
     * @brief 处理队列中的事件
     * @return 处理的事件数量
     */
    size_t processEvents();

    /**
     * @brief 刷新所有后端appender
     */
    void flushBackends();

    /**
     * @brief 解析配置属性
     * @param properties 配置属性
     */
    void parseProperties(const log4cplus::helpers::Properties& properties);

    // 禁用拷贝和赋值
    AcsAsyncAppender(const AcsAsyncAppender&) = delete;
    AcsAsyncAppender& operator=(const AcsAsyncAppender&) = delete;

private:
    // 配置参数
    size_t queue_size_;                    // 队列大小
    int flush_interval_ms_;                // 刷新间隔（毫秒）
    
    // 队列和线程
    std::unique_ptr<LogEventQueue> queue_; // 无锁队列
    std::unique_ptr<std::thread> worker_;  // 工作线程
    
    // 控制标志
    std::atomic<bool> shutdown_;           // 关闭标志
    std::atomic<bool> initialized_;        // 初始化标志
    
    // 统计信息
    std::atomic<uint64_t> dropped_count_;  // 丢弃的事件数量
    std::atomic<uint64_t> processed_count_;// 处理的事件数量
    
    // 批处理缓冲区
    static constexpr size_t BATCH_SIZE = 256;
    std::vector<log4cplus::spi::InternalLoggingEvent> batch_buffer_;
};

/**
 * @brief AcsAsyncAppender的智能指针类型
 */
using AcsAsyncAppenderPtr = log4cplus::helpers::SharedObjectPtr<AcsAsyncAppender>;

} // namespace acs
